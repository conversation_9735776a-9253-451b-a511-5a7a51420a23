import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from "@/components/ui/form";
import { CampaignData } from "@/lib/interfaces";
import { Textarea } from "../ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useEditCampaign } from "@/hooks/campaignHooks/use-edit-campaign";
import {
    Dialog,
    DialogDescription,
    DialogContent,
    DialogHeader,
    DialogTrigger,
    DialogTitle,
} from "../ui/dialog";
import { PencilLine } from "lucide-react";

const formSchema = z.object({
    accountid: z.number().nullable(),
    allowcreditapi: z.boolean(),
    apikey: z.string().nullable(),
    apikeyexpirydate: z.string().nullable(),
    callbackurl: z.string().nullable(),
    description: z.string().nullable(),
    enableotp: z.boolean(),
    enddate: z.string().nullable(),
    ipwhitelist: z.string().nullable(),
    isactive: z.boolean(),
    isenhanced: z.boolean(),
    itcapikey: z.string().nullable(),
    itcmerchantid: z.string().nullable(),
    itcproductid: z.string().nullable(),
    legacyusername: z.string().nullable(),
    payoutaccountid: z.number().nullable(),
    senderid: z.string().nullable(),
    settlementcallbackurl: z.string().nullable(),
    startdate: z.string().nullable(),
    transnotif: z.boolean(),
    transnotiftype: z.string().nullable(),
});

type FormValues = z.infer<typeof formSchema>;

function EditCampaignForm({ data }: { data: CampaignData }) {
    console.log("🚀 ~ EditCampaignForm ~ data:", data)
    const { toast } = useToast();
    const { editCampaign, isLoading: isEditLoading } = useEditCampaign();

    // console.log("edit data", data);

    const form = useForm<FormValues>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            accountid: data.accountid,
            allowcreditapi: data.allowcreditapi,
            apikey: data.apikey,
            apikeyexpirydate: data.apikeyexpirydate,
            callbackurl: data.callbackurl,
            description: data.description,
            enableotp: data.enableotp,
            enddate: data.enddate,
            ipwhitelist: data.ipwhitelist,
            isactive: data.isactive,
            isenhanced: data.isenhanced,
            itcapikey: data.itcapikey,
            itcmerchantid: data.itcmerchantid,
            itcproductid: data.itcproductid,
            legacyusername: data.legacyusername,
            payoutaccountid: data.payoutaccountid,
            senderid: data.senderid,
            settlementcallbackurl: data.settlementcallbackurl,
            startdate: data.startdate,
            transnotif: data.transnotif,
            transnotiftype: data.transnotiftype,
        },
    });

    const onSubmit = async (values: FormValues) => {
        editCampaign(
            { campaignId: data.id, updateData: values },
            {
                onSuccess: () => {
                    toast({ title: "Success", description: "Campaign updated successfully" });
                },
                onError: (error) => {
                    toast({
                        title: "Error",
                        description: error.message || "Failed to update campaign",
                    });
                },
            }
        );
    };

    return (
        <Dialog>
            <DialogTrigger asChild>
                <Button variant="icon">
                    <PencilLine className="h-4 w-4 text-green-500" />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-5xl !rounded-sm">
                <DialogHeader>
                    <DialogHeader className="my-2 border-b border-gray-200 dark:border-gray-700">
                        <DialogTitle className="mb-2 text-xl font-bold">Edit Campaign</DialogTitle>
                        <DialogDescription className="">
                            Add a new campaign for{" "}
                            <span className="font-bold uppercase text-primary">
                                {data.senderid}
                            </span>
                        </DialogDescription>
                    </DialogHeader>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">

                            <FormField
                                control={form.control}
                                name="legacyusername"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Legacy Username
                                        </FormLabel>
                                        <Input type="text" {...field} value={field.value || ""} />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="legacyusername"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Legacy Username
                                        </FormLabel>
                                        <Input type="text" {...field} value={field.value || ""} />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="itcmerchantid"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Merchant ID
                                        </FormLabel>
                                        <Input type="text" {...field} value={field.value || ""} />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="itcproductid"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Product ID
                                        </FormLabel>
                                        <Input type="text" {...field} value={field.value || ""} />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <FormField
                            control={form.control}
                            name="itcapikey"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="dark:text-white">API Key</FormLabel>
                                    <Input type="text" {...field} value={field.value || ""} />
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="description"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="dark:text-white">Description</FormLabel>
                                    <Textarea {...field} value={field.value || ""} />
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <div className="grid grid-cols-4 gap-4">
                            <FormField
                                control={form.control}
                                name="enableotp"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col items-center p-4">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Enable OTP</FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="isactive"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col items-center p-2">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Is Active</FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="isenhanced"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col items-center p-2">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">Is Enhanced</FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="allowcreditapi"
                                render={({ field }) => (
                                    <FormItem className="flex flex-col items-center p-2">
                                        <div className="space-y-0.5">
                                            <FormLabel className="text-base">
                                                Allow Credit API
                                            </FormLabel>
                                        </div>
                                        <FormControl>
                                            <Switch
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                            />
                                        </FormControl>
                                    </FormItem>
                                )}
                            />
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                            <FormField
                                control={form.control}
                                name="callbackurl"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Callback URL
                                        </FormLabel>
                                        <Input type="url" {...field} value={field.value || ""} />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="settlementcallbackurl"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel className="dark:text-white">
                                            Settlement Callback URL
                                        </FormLabel>
                                        <Input type="url" {...field} value={field.value || ""} />
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <FormField
                            control={form.control}
                            name="ipwhitelist"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="dark:text-white">IP Whitelist</FormLabel>
                                    <Input type="text" {...field} value={field.value || ""} />
                                    <FormDescription>
                                        Enter IP addresses separated by commas
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="payoutaccountid"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="dark:text-white">
                                        Payout Account ID
                                    </FormLabel>
                                    <Input
                                        type="number"
                                        {...field}
                                        onChange={(e) =>
                                            field.onChange(
                                                e.target.value ? Number(e.target.value) : null
                                            )
                                        }
                                        value={field.value || ""}
                                    />
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="transnotif"
                            render={({ field }) => (
                                <FormItem className="flex flex-row items-center justify-between p-2">
                                    <div className="space-y-0.5">
                                        <FormLabel className="text-base">
                                            Transaction Notification
                                        </FormLabel>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="transnotiftype"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel className="dark:text-white">
                                        Transaction Notification Type
                                    </FormLabel>
                                    <Input type="text" {...field} value={field.value || ""} />
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <Button
                            type="submit"
                            size="default"
                            className="w-full"
                            disabled={isEditLoading}
                        >
                            {isEditLoading ? (
                                <span className="flex items-center justify-center">
                                    <svg
                                        className="-ml-1 mr-3 h-5 w-5 animate-spin text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                    >
                                        <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"
                                        ></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8v8H4z"
                                        ></path>
                                    </svg>
                                    Updating...
                                </span>
                            ) : (
                                "Update Campaign"
                            )}
                        </Button>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}

export default React.memo(EditCampaignForm);
